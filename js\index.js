let container = document.querySelector(".container");
let select = document.querySelector("select");
const getVerse = document.querySelector("#getVerse");
let bibleVersion = select.value;
select.addEventListener("change", (e) => {
  bibleVersion = e.target.value;
  console.log(bibleVersion);
});
let paragraphText;
getVerse.addEventListener("click", async () => {
  await fetch(`https://bible-api.com/data/${bibleVersion}/random`)
    .then((response) => response.json())
    .then((data) => {
      if (paragraphText) {
        container.removeChild(paragraphText);
      }
      paragraphText = document.createElement("p");
      paragraphText.innerHTML = `${data.random_verse.book} ${data.random_verse.chapter}:${data.random_verse.verse} - ${data.random_verse.text}
      <br>
      <br>
      ${data.translation.name}`;
      console.log(data.random_verse.text);
      container.appendChild(paragraphText);
    });
});