let container = document.querySelector(".dropdown-menu");
let select = document.querySelector("select");
const getNews = document.querySelector("#getNews");
let bibleVersion = select.value;
select.addEventListener("change", (e) => {
  bibleVersion = e.target.value;
  console.log(bibleVersion);
});
let paragraphText;
getNews.addEventListener("click", async () => {
  await fetch(`https://newsapi.org/v2/top-headlines?country=us&category=business&apiKey=3f4bc424461c4358b560fab75b49df88`)
    .then((response) => response.json())
    .then((data) => {
      if (paragraphText) {
        container.removeChild(paragraphText);
      }
      paragraphText = document.createElement("p");
      paragraphText.innerHTML = `${data.random_verse.book} ${data.random_verse.chapter}:${data.random_verse.verse} - ${data.random_verse.text}
      <br>
      <br>
      ${data.translation.name}`;
      console.log(data.random_verse.text);
      container.appendChild(paragraphText);
    });
});