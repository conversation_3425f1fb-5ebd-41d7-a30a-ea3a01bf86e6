// News API Configuration
const API_KEY = '3f4bc424461c4358b560fab75b49df88';
const BASE_URL = 'https://newsapi.org/v2/top-headlines';

// DOM Elements
const newsContainer = document.querySelector('.hero-img');
const dropdownToggle = document.querySelector('.dropdown-toggle');
const dropdownLinks = document.querySelectorAll('.dropdown-menu a');

// Function to fetch news by category
async function fetchNews(category = 'general') {
  try {
    const response = await fetch(`${BASE_URL}?country=us&category=${category}&apiKey=${API_KEY}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    displayNews(data.articles);

  } catch (error) {
    console.error('Error fetching news:', error);
    displayError('Failed to fetch news. Please try again later.');
  }
}

// Function to display news articles
function displayNews(articles) {
  if (!articles || articles.length === 0) {
    displayError('No news articles found.');
    return;
  }

  // Clear existing content
  newsContainer.innerHTML = '';

  // Create news container
  const newsGrid = document.createElement('div');
  newsGrid.className = 'news-grid';

  // Display first 6 articles
  articles.slice(0, 6).forEach(article => {
    const newsCard = createNewsCard(article);
    newsGrid.appendChild(newsCard);
  });

  newsContainer.appendChild(newsGrid);
}

// Function to create individual news card
function createNewsCard(article) {
  const card = document.createElement('div');
  card.className = 'news-card';

  card.innerHTML = `
    <div class="news-image">
      <img src="${article.urlToImage || './img/placeholder.jpg'}" alt="${article.title}" onerror="this.src='./img/placeholder.jpg'">
    </div>
    <div class="news-content">
      <h3 class="news-title">${article.title}</h3>
      <p class="news-description">${article.description || 'No description available.'}</p>
      <div class="news-meta">
        <span class="news-source">${article.source.name}</span>
        <span class="news-date">${new Date(article.publishedAt).toLocaleDateString()}</span>
      </div>
      <a href="${article.url}" target="_blank" class="read-more">Read More</a>
    </div>
  `;

  return card;
}

// Function to display error messages
function displayError(message) {
  newsContainer.innerHTML = `
    <div class="error-message">
      <h2>Oops!</h2>
      <p>${message}</p>
    </div>
  `;
}

// Event listeners for dropdown menu
dropdownLinks.forEach(link => {
  link.addEventListener('click', (e) => {
    e.preventDefault();

    // Get category from href
    const category = link.getAttribute('href').substring(1); // Remove #

    // Map categories to API categories
    const categoryMap = {
      'breaking': 'general',
      'sports': 'sports',
      'technology': 'technology',
      'business': 'business',
      'entertainment': 'entertainment',
      'health': 'health'
    };

    const apiCategory = categoryMap[category] || 'general';
    fetchNews(apiCategory);

    // Update dropdown text
    dropdownToggle.innerHTML = `${link.textContent} <i class="fa-solid fa-angle-down"></i>`;
  });
});

// Load general news on page load
document.addEventListener('DOMContentLoaded', () => {
  fetchNews('general');
});