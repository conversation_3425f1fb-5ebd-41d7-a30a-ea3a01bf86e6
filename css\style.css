@import url('https://fonts.googleapis.com/css2?family=Nexa:wght@300;400;700;900&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Nexa', sans-serif;
}

.nav-bar{
    background: #0000007c;
    backdrop-filter: blur(10px);
    color: #fff;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-logo img{
    height: 50px;
}

.nav-links{
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-links a{
    text-decoration: none;
    color: #fff;
    font-size: 1.2rem;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 100px;
    border: 0px solid transparent;
    padding: 10px 20px;
    border-radius: 100px;
}

.nav-links a:hover{
    padding: 10px 20px;
    background: rgba(3, 239, 158, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 0px solid #03EF9E;
    border-radius: 100px;
    color: #03EF9E;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    cursor: pointer;
    user-select: none;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    padding: 10px 0;
    margin-top: 10px;
}

.dropdown-menu a {
    display: block;
    padding: 12px 20px;
    color: #fff;
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s ease;
    border-radius: 0;
}

.dropdown-menu a:hover {
    background: rgba(3, 239, 158, 0.2);
    color: #03EF9E;
    padding-left: 30px;
    border: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#btn{
    margin-left: auto;
}

.btn{
    align-items: flex-end;
    background: rgba(255, 255, 255, 0.1);
    /* backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); */
    padding: 10px 20px;
    border-radius: 100px;
    border: 1px solid #fff ;
    cursor: pointer;
    color: #fff;
}

.btn:hover{
    background: rgba(3, 239, 158, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid #03EF9E;
    color: #03EF9E;
}