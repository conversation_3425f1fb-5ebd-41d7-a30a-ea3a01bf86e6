@import url('https://fonts.googleapis.com/css2?family=Nexa:wght@300;400;700;900&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Nexa', sans-serif;
}

.nav-bar{
    background: #0000007c;
    backdrop-filter: blur(10px);
    color: #fff;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-logo img{
    height: 50px;
}

.nav-links{
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-links a{
    text-decoration: none;
    color: #fff;
    font-size: 1.2rem;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 100px;
    border: 0px solid transparent;
    padding: 10px 20px;
    border-radius: 100px;
}

.nav-links a:hover{
    padding: 10px 20px;
    background: rgba(3, 239, 158, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 0px solid #03EF9E;
    border-radius: 100px;
    color: #03EF9E;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    cursor: pointer;
    user-select: none;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    padding: 10px 0;
    margin-top: 10px;
}

.dropdown-menu a {
    display: block;
    padding: 12px 20px;
    color: #fff;
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s ease;
    border-radius: 0;
}

.dropdown-menu a:hover {
    background: rgba(3, 239, 158, 0.2);
    color: #03EF9E;
    padding-left: 30px;
    border: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#btn{
    margin-left: auto;
}

.btn{
    align-items: flex-end;
    background: rgba(255, 255, 255, 0.1);
    /* backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); */
    padding: 10px 20px;
    border-radius: 100px;
    border: 1px solid #fff ;
    cursor: pointer;
    color: #fff;
}

.btn:hover{
    background: rgba(3, 239, 158, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid #03EF9E;
    color: #03EF9E;
}

/* News Grid Styles */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    padding: 50px;
    max-width: 1400px;
    margin: 0 auto;
}

.news-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    color: #fff;
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: #03EF9E;
}

.news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-content {
    padding: 20px;
}

.news-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.4;
    color: #fff;
}

.news-description {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.8);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.news-source {
    font-weight: 600;
    color: #03EF9E;
}

.read-more {
    display: inline-block;
    padding: 8px 16px;
    background: rgba(3, 239, 158, 0.2);
    color: #03EF9E;
    text-decoration: none;
    border-radius: 20px;
    border: 1px solid #03EF9E;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.read-more:hover {
    background: #03EF9E;
    color: #000;
    transform: translateX(5px);
}

.error-message {
    text-align: center;
    padding: 50px;
    color: #fff;
}

.error-message h2 {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #ff6b6b;
}

.error-message p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
}